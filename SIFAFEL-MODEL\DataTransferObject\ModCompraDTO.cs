﻿using System;
using Newtonsoft.Json;
using SIFAFEL_MODEL.Data_Model_API;

namespace SIFAFEL_MODEL.DataTransferObject
{
    public class ModCompraDTO
    {
        public int id_compra { get; set; }
        public string tipo { get; set; }
        public int? id_referencia { get; set; }
        public string num_poliza { get; set; }
        public int? id_pais_origen { get; set; }
        public int? id_pais_destino { get; set; }
        public int? id_sucursal { get; set; }
        public int? id_usuario { get; set; }
        public int id_moneda { get; set; }
        public int id_proveedor { get; set; }
        public double imp_total { get; set; }
        public string imp_total_txt { get; set; }
        public DateTime? fecha_compra { get; set; }
        public DateTime? fecha_registro { get; set; }
        public int? id_usu_registro { get; set; }
        public Enum.Estado estado { get; set; }

        public DirPaisDTO tbl_dir_pais_origen { get; set; }
        public DirPaisDTO tbl_dir_pais_destino { get; set; }
        public ModProveedorDTO tbl_mod_proveedor { get; set; }
        public ModSucursalDTO tbl_mod_sucursal { get; set; }
        public SecUsuarioDTO tbl_sec_usuario { get; set; }
    }
    public class FiltroComprasDTO
    {
        public int? id_contribuyente { get; set; }
        public int? id_sucursal { get; set; }
        [JsonConverter(typeof(CustomDateConverter))]
        public DateTime fecha_inicio { get; set; }
        [JsonConverter(typeof(CustomDateConverter))]
        public DateTime fecha_final { get; set; }
        public string estado { get; set; }
        public int? id_proveedor { get; set; }
        public string nit_proveedor { get; set; }
        public int? id_moneda { get; set; }
    }


}
