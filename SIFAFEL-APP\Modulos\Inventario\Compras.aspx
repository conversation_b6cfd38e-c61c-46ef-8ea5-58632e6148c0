<%@ Page Title="" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="Compras.aspx.cs" Inherits="SIFAFEL_APP.Modulos.Inventario.Compras" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">
    <style type="text/css">
        .dataTables_length {
            display: none;
        }

        /* Estilos para el botón de búsqueda de proveedor */
        .add-newplus {
            margin-bottom: 5px;
        }

        .add-newplus a {
            color: #007bff;
            text-decoration: none;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .add-newplus a:hover {
            color: #0056b3;
            text-decoration: underline;
        }

        .plus-down-add {
            width: 14px;
            height: 14px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">
    <div class="page-header">
        <div class="add-item d-flex">
            <div class="page-title">
                <h4>Listado de compras</h4>
                <h6>Administra las compras</h6>
            </div>
        </div>
        <div class="page-btn">
            <a href="NuevaCompra.aspx" class="btn btn-added color">
                <i data-feather="plus-circle" class="me-2"></i>Nueva compra
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">

            <div class="row">
                <%--<div class="col-12 col-md-6 col-lg-3 mb-2">
                    <div class="input-group input-group-sm">
                        <span class="input-group-text">
                            <i data-feather="airplay" class="feather-16"></i>
                        </span>
                       <select id="ddlCaja" name="ddlCaja" class="form-select select-sm" <%= (lstCajas != null && lstCajas.Count > 1) ? "" : "disabled='disabled'" %>>
                            <% if (lstCajas != null)
                                {
                                    foreach (var caja in lstCajas)
                                    { %>
                            <option value="<%= caja.id %>"><%= caja.nombre %></option>
                            <% }
                                }%>
                        </select>
                    </div>
                </div>--%>
                <div class="col-12 col-md-6 col-lg-3 mb-2">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i data-feather="calendar" class="feather-16"></i>
                        </span>
                        <input type="text" id="txtRangoFecha" class="form-control" />
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-2 mb-2">
                    <div class="input-group input-group-sm">
                        <span class="input-group-text">
                            <i data-feather="refresh-ccw" class="feather-16"></i>
                        </span>
                        <select id="ddlEstados" class="form-select select-sm">
                            <option value="">Todos</option>
                            <option value="COM">Compras</option>
                            <option value="DUC">Ducas</option>
                        </select>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-3 mb-2">
                    <div class="add-newplus">
                        <a href="#!" id="btnBuscarProveedor">
                            <i data-feather="search" class="plus-down-add"></i>
                            <span>Buscar</span>
                        </a>
                    </div>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i data-feather="user" class="feather-16"></i>
                        </span>
                        <input id="txtNITProveedor" class="form-control" placeholder="NIT" />
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-1 mb-2 d-grid">
                    <button id="btnBuscarCompras" type="button" href="#" class="btn btn-success btn-sm p-1">
                        <i data-feather="search" class="me-2"></i>
                    </button>
                </div>
            </div>

            <div class="table-responsive">

                <table id="grvCompras" class="table">
                    <thead>
                        <tr>
                            <th>No.</th>
                            <th>Fecha</th>
                            <th>Factura</th>
                            <th>Estado</th>
                            <th>Usuario</th>
                            <th>Total</th>
                            <th>Opciones</th>
                        </tr>
                    </thead>
                    <%--<tbody class="sales-list"></tbody>--%>
                </table>
            </div>
        </div>
    </div>

    <%-- MODAL PROVEEDORES --%>
    <div class="modal fade effect-scale" id="modalProveedores">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h4 class="modal-title"><i data-feather="truck" class="feather-16"></i>&nbsp;Consultas de proveedores</h4>
                    <button type="button" aria-label="Close" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-start">

                    <table id="grvProveedores" class="table table-bordered table-striped table-condensed table-sm">
                        <thead class="table-head">
                            <tr>
                                <th>Código</th>
                                <th>NIT</th>
                                <th>Proveedor</th>
                                <th>Teléfono</th>
                                <th>Correo</th>
                                <th>Direccion</th>
                                <th>&nbsp;</th>
                            </tr>
                        </thead>
                    </table>

                </div>
                <div class="modal-footer d-flex justify-content-end">
                    <button type="button" class="btn btn-cancel btn-sm" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        // Variables globales
        var tbl_proveedores;

        $(document).ready(function () {
            var start = moment().subtract(30, 'days');
            var end = moment();

            $('#txtRangoFecha').daterangepicker({
                startDate: start,
                endDate: end,
                maxDate: moment(),
                locale: {
                    format: 'DD/MM/YYYY',
                    applyLabel: "Aplicar",
                    cancelLabel: "Cancelar",
                    fromLabel: "Desde",
                    toLabel: "Hasta",
                    customRangeLabel: "Personalizado"
                }
            });

            // Inicializar DataTable de proveedores
            inicializarDataTableProveedores();

            // Evento para buscar proveedores
            $('#btnBuscarProveedor').on('click', function () {
                cargarProveedores();
            });

            $('#grvCompras').DataTable({
                "language": {
                    "emptyTable": "No hay datos disponibles en la tabla",
                    "zeroRecords": "No se encontraron registros coincidentes",
                    "loadingRecords": "Cargando...",
                    "processing": "Procesando...",
                    "search": ' ',
                    "lengthMenu": "",
                    "searchPlaceholder": "Buscar",
                    "info": " ",
                    "paginate": {
                        next: ' <i class="fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                layout: {
                    topStart: {
                        buttons: ['copy', 'excel', 'pdf', 'colvis']
                    }
                },
                "columnDefs": [
                    {
                        "targets": [0, 3],
                        "className": "text-center ref-number"
                    },
                    {
                        "targets": [3],
                        "className": "text-center"
                    },
                    {
                        "targets": [6],
                        "className": "action-table-data"
                    }
                ],
                order: [[0, 'desc']],
                "paging": true, // Habilita la paginación
                "searching": false, // Desactiva la búsqueda
                "info": false, // Desactiva la información de la tabla
                "autoWidth": false,
                "lengthChange": true, // Muestra el control de "mostrar entradas"
                "lengthMenu": [10, 25, 50, 100], // Opciones para mostrar entradas
                "columns": [
                    {
                        "data": function (item) {
                            return `#${item.id_compra}`;
                        }
                    },
                    {
                        "data": "fecha_registro",
                        "render": function (data, type, row) {
                            if (!data) return ""; // Manejo de valores nulos o vacíos

                            let date = new Date(data);
                            return date.toLocaleString("es-ES", {
                                year: "numeric",
                                month: "2-digit",
                                day: "2-digit",
                                hour: "2-digit",
                                minute: "2-digit",
                                hour12: false // Asegura el formato de 24 horas
                            });
                        }
                    },
                    {
                        "data": function (item) {
                            return `${item.num_factura}/${item.num_serie}`;
                        }
                    },
                    {
                        "data": function (item) {
                            return `<span class="${item.class_color}">${item.nom_estado}</span>`;
                        }
                    },
                    {
                        data: function (item) {
                            return `<div class="userimgname">
										<a href="#!" class="product-img">
											<img src="<%=ConfigurationManager.AppSettings["url_cdn"] %>${item.usuario_img_perfil}" alt="">
										</a>
										<a href="#!">${item.nombre_usuario}</a>
									</div>`;
                        }
                    },
                    //{
                    //    data: "imp_recargo",
                    //    render: function (data, type, row, meta) {
                    //        return `<span class="tbl_txt_precio">${MoneyFormat(row.moneda, data)}</span>`;
                    //    }
                    //},
                    //{
                    //    data: "imp_descuento",
                    //    render: function (data, type, row, meta) {
                    //        return `<span class="tbl_txt_precio">${MoneyFormat(row.moneda, data)}</span>`;
                    //    }
                    //},
                    {
                        data: "imp_total",
                        render: function (data, type, row, meta) {
                            return `<span class="tbl_txt_precio">${MoneyFormat(row.moneda, data)}</span>`;
                        }
                    },

                    {
                        "data": null,
                        "orderable": false,
                        "className": "text-center",
                        "render": function (data, type, row) {
                            return `<div class="edit-delete-action data-row">
                                        ${row.cod_estado !== 'COM' ? `
                                            <a class="me-2 p-2 mb-0" href="./NuevaCompra.aspx?id=${row.id_compra}">
                                                <i data-feather="edit" class="feather-edit"></i>
                                            </a>
                                        `: ''}
                                        <a class="me-2 p-2 mb-0 print-file" href="#!">
                                            <i data-feather="printer" class="action-eye"></i>
                                        </a>
                                        <a class="me-2 p-2 mb-0" href="#!">
                                            <i data-feather="eye" class="info-img"></i>
                                        </a>
                                        <a class="me-2 confirm-text p-2 mb-0 ${row.cod_estado === 'ANU' ? 'btn disabled' : ''}" href="#!">
                                            <i data-feather="trash-2"></i>
                                        </a>
                                    </div>
                                `;
                        }
                    }


                ],
                drawCallback: function () {
                    feather.replace();
                },
                createdRow: function (row, data, indexRow) {
                    $(row).find(".print-file").on("click", function () {
                        console.log(`print`);

                        $.ajax({
                            url: `${_url_redirect}Controllers/venta.ashx?mth=${mtdEnc("get/rpt")}&id=${data.secret_key}`,
                            type: 'GET',
                            beforeSend: function () {
                                __Progress("Generando documento...");
                            },
                            success: function (response) {
                                console.log(response);
                                if (response) {
                                    base64_file(
                                        base64str = response.base64_content,
                                        fileName = response.name,
                                        contentType = response.content_type);
                                }
                            },
                            complete: function () {
                                __ProgressOff();
                            }
                        });
                    });
                }
            });

            $('#btnBuscarCompras').on('click', function (e) {
                e.preventDefault();

                var rangoFecha = $('#txtRangoFecha').val();
                var estados = $('#ddlEstados').val();
                var nitCliente = $('#txtNITProveedor').val();

                if (!rangoFecha) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Advertencia',
                        text: 'Por favor, ingrese un rango de fechas.'
                    });
                    return;
                }

                var fechas = rangoFecha.split(' - ');
                var fechaInicio = fechas[0];
                var fechaFin = fechas[1];

                var datos = {
                    fecha_inicio: fechaInicio,
                    fecha_final: fechaFin,
                    estado: estados,
                    nit_proveedor: nitCliente || "",
                    id_moneda: ""
                };

                var table = $('#grvCompras').DataTable();
                __Progress("Consultando...");

                var frmData = new FormData();
                frmData.append("mth", mtdEnc("get/purchases"));
                frmData.append("data", mtdEnc(JSON.stringify(datos)));

                $.ajax({
                    type: 'POST',
                    url: `${_url_redirect}Controllers/compra.ashx`,
                    data: frmData,
                    contentType: false,
                    processData: false,
                    beforeSend: function () {
                        table.clear();
                    },
                    success: function (response) {
                        if (response.data) {
                            table.clear().rows.add(response.data).draw();
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log('Error:', error);
                    },
                    complete: function () {

                        __ProgressOff();
                    }
                });

            });

            setTimeout(function () {
                $("#btnBuscarCompras").click();
            });

        });

        // Función para inicializar DataTable de proveedores
        function inicializarDataTableProveedores() {
            tbl_proveedores = $('#grvProveedores').DataTable({
                "language": {
                    "lengthMenu": "_MENU_",
                    "sSearch": "",
                    "searchPlaceholder": "Buscar proveedor",
                    "sLoadingRecords": "Cargando registros...",
                    "info": " ",
                    paginate: {
                        next: ' <i class="fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                info: false,
                paging: true,
                autoWidth: false,
                columnDefs: [
                    {
                        targets: [0, 3, 4],
                        visible: false
                    }
                ],
                columns: [
                    { data: "id_proveedor" },
                    { data: "nit" },
                    { data: "nombres" },
                    { data: "telefono" },
                    {
                        data: function (item) {
                            return item.email || item.correo || '';
                        }
                    },
                    { data: "direccion" },
                    {
                        data: function (item) {
                            return `<td>
                                        <div class="hstack gap-2 fs-15">
                                            <a href="#!" class="btn btn-icon btn-sm btn-soft-success rounded-pill add-proveedor"><i class="fas fa-user-check"></i></a>
                                        </div>
                                    </td>`;
                        }
                    }
                ],
                createdRow: function (row, data, dataIndex) {
                    let $row = $(row);

                    $row.find(".add-proveedor").on("click", function () {
                        agregarProveedor($(this), data);
                    });

                    $row.on("dblclick", function () {
                        agregarProveedor($(this), data);
                    });
                }
            });
        }

        function cargarProveedores() {
            __Progress("Consultando proveedores...");

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                type: 'POST',
                data: function () {
                    var fmAuth = new FormData();
                    fmAuth.append("mth", mtdEnc("get/proveedores"));
                    fmAuth.append("data", mtdEnc(JSON.stringify({})));
                    return fmAuth;
                }(),
                contentType: false,
                processData: false,
                beforeSend: function () {
                },
                success: function (result) {
                    if (result.type == "success") {
                        $('#grvProveedores').dataTable().fnClearTable();
                        $('#grvProveedores').DataTable().search("").draw();
                        $('#grvProveedores').dataTable().fnAddData(result.data);

                        // Ocultar progress y mostrar modal
                        __ProgressOff();
                        $("#modalProveedores").modal("show");
                    }
                    else if (result.type == "warning") {
                        // Ocultar progress en caso de warning
                        __ProgressOff();
                        Swal.fire({
                            icon: 'warning',
                            title: 'Advertencia',
                            text: result.message
                        });
                    }
                    else {
                        // Ocultar progress en caso de error
                        __ProgressOff();
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: result.message
                        });
                    }
                },
                error: function (xhr, status, error) {
                    // Ocultar progress en caso de error
                    __ProgressOff();
                    console.error('Error al cargar proveedores:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Error al cargar los proveedores'
                    });
                }
            });
        }

        // Función para seleccionar proveedor del modal
        function seleccionarProveedor(id, nit, nombre) {
            $('#txtNITProveedor').val(nit);
            $('#modalProveedores').modal('hide');
        }

        // Función para agregar proveedor
        function agregarProveedor($btn, data) {
            $btn.html('<span class="fas fa-spinner fa-spin"></span>');
            seleccionarProveedor(data.id_proveedor, data.nit, data.nombres);

            setTimeout(function () {
                $btn.html('<i class="fas fa-user-check"></i>');
                $("#modalProveedores").modal("hide");
            }, 200);
        }
    </script>

</asp:Content>
